package ltd.zstech.standard.service;

import com.alibaba.fastjson.JSONObject;
import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.modules.commonservice.service.FormOperationService;
import ltd.zstech.standard.entity.DocumentSection;
import ltd.zstech.standard.util.AppendixTextProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
public class FileOperationService {

    @Value("${jeecg.path.upload}")
    private String path;

    @Resource
    private FormOperationService formOperationService;

    static {
        registerWord2412();
    }

    /**
     * 按标题切分Word文档（改进版，专门处理WPS文档的样式和编号问题）
     * 同时提取文档目录信息
     */
    public JSONObject splitDocumentByHeadingsWithTOC(MultipartFile file, Integer templateId, Integer formdataId) {
        JSONObject result = new JSONObject();
        List<JSONObject> splitDocuments = new ArrayList<>();
        List<JSONObject> tableOfContents = new ArrayList<>();

        try {
            // 加载源文档
            Document sourceDoc = new Document(file.getInputStream());

            // 首先提取目录信息
            JSONObject tocResult = extractTableOfContents(sourceDoc);
            tableOfContents = tocResult.getJSONArray("flatToc").toJavaList(JSONObject.class);

            // 获取文档中的所有Body节点（包含段落和表格等）
            NodeCollection bodies = sourceDoc.getChildNodes(NodeType.BODY, true);
            List<DocumentSection> sections = new ArrayList<>();
            List<Node> allBodyNodes = new ArrayList<>();
            List<Paragraph> headingParagraphs = new ArrayList<>();

            // 收集所有Body中的直接子节点（段落、表格等）
            for (int bodyIndex = 0; bodyIndex < bodies.getCount(); bodyIndex++) {
                Body body = (Body) bodies.get(bodyIndex);
                NodeCollection bodyChildren = body.getChildNodes(NodeType.ANY, false);

                for (int i = 0; i < bodyChildren.getCount(); i++) {
                    Node node = bodyChildren.get(i);
                    allBodyNodes.add(node);

                    // 如果是段落，检查是否为标题
                    if (node.getNodeType() == NodeType.PARAGRAPH) {
                        Paragraph para = (Paragraph) node;
                        log.info("样式：{} - 内容：{}", para.getParagraphFormat().getStyle().getName(), para.getText().trim());
                        if (isHeadingLevel(para)) {
                            headingParagraphs.add(para);
                        }
                    } else if (node.getNodeType() == NodeType.TABLE) {
                        log.info("发现表格节点");
                    }
                }
            }

            // 处理首页内容（第一个标题之前的所有内容）
            if (!headingParagraphs.isEmpty()) {
                Paragraph firstHeading = headingParagraphs.get(0);
                int firstHeadingIndex = allBodyNodes.indexOf(firstHeading);

                if (firstHeadingIndex > 0) {
                    List<Node> frontPageContent = new ArrayList<>();
                    // 收集第一个标题之前的所有内容作为首页
                    for (int j = 0; j < firstHeadingIndex; j++) {
                        frontPageContent.add(allBodyNodes.get(j));
                    }

                    if (!frontPageContent.isEmpty()) {
                        DocumentSection frontPageSection = new DocumentSection("首页", null, frontPageContent);
                        sections.add(frontPageSection);
                        log.info("创建首页段落，包含 {}个节点", frontPageContent.size());
                    }
                }
            }

            // 为每个标题创建段落
            for (int i = 0; i < headingParagraphs.size(); i++) {
                Paragraph headingPara = headingParagraphs.get(i);
                List<Node> sectionContent = new ArrayList<>();

                // 找到当前标题在所有节点中的位置
                int startIndex = allBodyNodes.indexOf(headingPara);
                if (startIndex == -1) continue;

                // 添加当前标题段落
                sectionContent.add(headingPara);

                // 查找下一个标题的位置
                int nextHeadingIndex = allBodyNodes.size();
                if (i < headingParagraphs.size() - 1) {
                    Paragraph nextHeading = headingParagraphs.get(i + 1);
                    int nextIndex = allBodyNodes.indexOf(nextHeading);
                    if (nextIndex != -1) {
                        nextHeadingIndex = nextIndex;
                    }
                }

                // 添加从当前标题后到下一个标题前的所有节点（包括表格）
                for (int j = startIndex + 1; j < nextHeadingIndex; j++) {
                    sectionContent.add(allBodyNodes.get(j));
                }

                DocumentSection section = new DocumentSection(headingPara.getText().trim(), headingPara, sectionContent);
                sections.add(section);

                log.info("创建段落: {}, 包含 {}个节点", headingPara.getText().trim(), sectionContent.size());
            }

            // 生成拆分后的文档
            splitDocuments = createSplitDocuments(sections, sourceDoc, templateId, formdataId);

            // 组装返回结果
            result.put("splitDocuments", splitDocuments);
            result.put("tableOfContents", tableOfContents);
            result.put("totalSections", sections.size());

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("文档拆分完成，共生成 {} 个文档，提取 {} 个目录项", splitDocuments.size(), tableOfContents.size());
        return result;
    }

    /**
     * 按标题切分Word文档（原版方法，保持向后兼容）
     */
    public List<JSONObject> splitDocumentByHeadings(MultipartFile file, Integer templateId, Integer formdataId) {
        JSONObject result = splitDocumentByHeadingsWithTOC(file, templateId, formdataId);
        return result.getJSONArray("splitDocuments").toJavaList(JSONObject.class);
    }

    /**
     * 从文档中提取目录信息（基于标题样式，用于文档拆分）
     */
    private JSONObject extractTableOfContents(Document sourceDoc) {
        List<JSONObject> tableOfContents = new ArrayList<>();
        try {
            // 获取文档中的所有段落
            NodeCollection paragraphs = sourceDoc.getChildNodes(NodeType.PARAGRAPH, true);

            log.info("开始提取文档目录，共找到 {} 个段落", paragraphs.getCount());

            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph para = (Paragraph) paragraphs.get(i);

                // 检查是否为标题段落
                if (isHeadingLevel(para)) {
                    JSONObject tocEntry = new JSONObject();
                    String title = para.getText().trim();

                    // 获取标题级别
                    int headingLevel = getHeadingLevel(para);

                    // 获取页码（如果有的话）
                    String pageNumber = extractPageNumber(para);

                    tocEntry.put("title", title);
                    tocEntry.put("level", headingLevel);
                    tocEntry.put("pageNumber", pageNumber);
                    tocEntry.put("index", i);

                    tableOfContents.add(tocEntry);

                    log.info("发现目录项: 级别{} - {} (页码: {})", headingLevel, title, pageNumber);
                }
            }

            log.info("目录提取完成，共找到 {} 个目录项", tableOfContents.size());

        } catch (Exception e) {
            log.error("提取文档目录时出错: {}", e.getMessage());
        }

        // 构建层级结构
        List<JSONObject> hierarchicalToc = buildHierarchicalStructure(tableOfContents);

        JSONObject result = new JSONObject();
        result.put("flatToc", tableOfContents);
        result.put("hierarchicalToc", hierarchicalToc);
        result.put("totalItems", tableOfContents.size());

        return result;
    }

    private List<JSONObject> createSplitDocuments(List<DocumentSection> sections, Document originalDoc, Integer templateId, Integer formdataId) throws Exception {
        log.info("开始创建 {} 个拆分文档", sections.size());
        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < sections.size(); i++) {
            try {
                log.info("正在处理第 {} 个段落: {}", i + 1, sections.get(i).Title);

                // 创建新文档
                Document newDoc = new Document();
                newDoc.removeAllChildren();

                // 复制原文档的所有样式（包括表格样式）
                copyAllStyles(originalDoc, newDoc);

                // 创建新的段落
                Section newSection = new Section(newDoc);
                Body newBody = new Body(newDoc);
                newSection.appendChild(newBody);
                newDoc.appendChild(newSection);

                // 确定内容范围
                List<Node> contentNodes = getSectionContent(sections, i);
                log.info("段落包含 {} 个节点", contentNodes.size());

                // 复制内容到新文档
                for (Node node : contentNodes) {
                    try {
                        log.info("正在导入节点类型: {}", node.getNodeType());
                        Node importedNode = newDoc.importNode(node, true);
                        newBody.appendChild(importedNode);

                        if (node.getNodeType() == NodeType.TABLE) {
                            log.info("成功导入表格节点");
                        }
                    } catch (Exception ex) {
                        log.info("节点 {} 导入失败: {}", node.getNodeType(), ex.getMessage());
                        ex.printStackTrace();
                    }
                }

                // 生成文件名，增加错误处理
                String sectionTitle = sections.get(i).Title;
                if (sectionTitle == null || sectionTitle.trim().isEmpty()) {
                    sectionTitle = "未命名段落_" + i;
                }

                String fileName;
                // 特殊处理附录文件名
                if (AppendixTextProcessor.isAppendixContent(sectionTitle)) {
                    fileName = AppendixTextProcessor.generateAppendixFileName(sectionTitle) + "_" + i;
                } else {
                    fileName = sanitizeFileName(sectionTitle + " - " + i);
                }

                if (fileName == null || fileName.trim().isEmpty()) {
                    fileName = "document_section_" + i;
                }

                String filePath = Paths.get(path + File.separator + templateId + File.separator + formdataId, fileName + ".docx").toString();

                // 保存文档
                newDoc.save(filePath);
                log.info("已保存: {}", filePath);

                // 清理内存
                contentNodes.clear();
                contentNodes = null;

                JSONObject obj = new JSONObject();
                obj.put("fileName", fileName);
                obj.put("filePath", templateId + "/" + formdataId + "/" + fileName + ".docx");
                result.add(obj);
            } catch (Exception ex) {
                log.info("处理第 {} 个段落时出错: {}", i + 1, ex.getMessage());
                ex.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 复制所有样式（包括表格样式）
     */
    private void copyAllStyles(Document sourceDoc, Document targetDoc) {
        try {
            // 复制所有样式
            for (Style style : sourceDoc.getStyles()) {
                try {
                    // 检查目标文档中是否已存在该样式
                    boolean exists = false;
                    for (Style existingStyle : targetDoc.getStyles()) {
                        if (existingStyle.getName().equals(style.getName())) {
                            exists = true;
                            break;
                        }
                    }

                    if (!exists) {
                        targetDoc.getStyles().addCopy(style);
                        log.info("复制样式: {}", style.getName());
                    }
                } catch (Exception ex) {
                    log.info("样式{}复制失败: {}", style.getName(), ex.getMessage());
                }
            }

            // 复制列表定义（用于编号和项目符号）
            for (int i = 0; i < sourceDoc.getLists().getCount(); i++) {
                try {
                    targetDoc.getLists().addCopy(sourceDoc.getLists().get(i));
                } catch (Exception ex) {
                    log.info("列表定义复制失败: {}", ex.getMessage());
                }
            }

        } catch (Exception ex) {
            log.info("样式复制过程中出错: {}", ex.getMessage());
        }
    }

    private List<Node> getSectionContent(List<DocumentSection> sections, int currentIndex) {
        // 直接返回已经收集好的内容
        return sections.get(currentIndex).Content;
    }


    /**
     * 清理文件名
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unnamed_document";
        }

        // 先去除空格
        String cleanName = fileName.replaceAll("\\s+", "");

        // 替换非法字符为下划线
        cleanName = cleanName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_");

        // 合并连续的下划线
        cleanName = cleanName.replaceAll("_{2,}", "_");

        // 去除开头和结尾的下划线
        cleanName = cleanName.replaceAll("^_+|_+$", "");

        // 如果清理后为空，使用默认名称
        if (cleanName.isEmpty()) {
            cleanName = "unnamed_document";
        }

        // 限制长度，使用处理后的字符串长度
        return cleanName.substring(0, Math.min(cleanName.length(), 50));
    }

    /**
     * 获取文档目录页中的所有目录
     * 专门解析目录页中的目录项及其层级关系，返回层级结构数据
     *
     * @param file 上传的文档文件
     * @return 包含层级结构的JSON对象
     */
    public JSONObject getDocumentTableOfContents(MultipartFile file) {
        JSONObject result = new JSONObject();
        List<JSONObject> flatToc = new ArrayList<>();

        try {
            // 加载源文档
            Document sourceDoc = new Document(file.getInputStream());

            // 查找目录页
            List<Paragraph> tocPageParagraphs = findTableOfContentsPage(sourceDoc);

            if (tocPageParagraphs.isEmpty()) {
                log.warn("未找到目录页");
                result.put("flatToc", flatToc);
                result.put("hierarchicalToc", new ArrayList<>());
                result.put("totalItems", 0);
                return result;
            }

            log.info("找到目录页，共 {} 个段落", tocPageParagraphs.size());

            // 解析目录页中的目录项
            for (int i = 0; i < tocPageParagraphs.size(); i++) {
                Paragraph para = tocPageParagraphs.get(i);

                // 获取段落的纯文本内容，过滤域代码
                String text = extractCleanText(para);

                // 添加调试日志
                log.debug("处理段落 {}: 原始文本=[{}], 清理后文本=[{}]", i, para.getText(), text);

                // 跳过空行和"目次"标题行
                if (text.isEmpty() || text.equals("目次") || text.equals("目录") || text.equals("TABLEOFCONTENTS")) {
                    log.debug("跳过段落 {}: 空行或标题行", i);
                    continue;
                }

                // 解析目录项
                JSONObject tocEntry = parseTocEntry(text, i);
                if (tocEntry != null) {
                    flatToc.add(tocEntry);
                    log.info("解析目录项: 级别{} - {} (页码: {})",
                            tocEntry.getInteger("level"),
                            tocEntry.getString("title"),
                            tocEntry.getString("pageNumber"));
                } else {
                    log.debug("段落 {} 未被识别为目录项: [{}]", i, text);
                }
            }

            // 构建层级结构
            List<JSONObject> hierarchicalToc = buildHierarchicalStructure(flatToc);

            result.put("flatToc", flatToc);
            result.put("hierarchicalToc", hierarchicalToc);
            result.put("totalItems", flatToc.size());

            log.info("目录解析完成，共找到 {} 个目录项", flatToc.size());

        } catch (Exception e) {
            log.error("解析文档目录时出错: {}", e.getMessage());
            throw new RuntimeException("解析文档目录失败", e);
        }

        return result;
    }

    /**
     * 测试目录项识别（用于调试）
     */
    public void testTocPatternRecognition() {
        String[] testTexts = {
                "前言 ................................................................ II",
                "前  言 ................................................................ II",
                "TOC \\o \"1-2\" \\z \\u 前言",
                "1 范围 ................................................................ 1",
                "6.1 设备分级 ........................................................ 2",
                "附录 .................................................................. 4",
                "附录A 设备清单 .................................................... 5",
                "附录B 操作流程图 .................................................. 6",
                "Appendix A Equipment List .......................................... 7",
                "A 设备清单 .......................................................... 8",
                "A.1 主要设备 ........................................................ 9",
                "TOC \\o \"1-3\" \\h \\z \\u 1 范围"
        };

        for (String text : testTexts) {
            // 先清理文本
            String cleanText = cleanTocText(text);
            boolean isValid = containsValidTocPattern(cleanText);
            String pageNumber = extractTocPageNumber(cleanText);
            String title = extractTocTitle(cleanText, pageNumber);
            int level = determineTocLevel(cleanText);
            log.info("测试文本: [{}] -> 清理后: [{}] -> 有效: {}, 级别: {}, 页码: [{}], 标题: [{}]",
                    text, cleanText, isValid, level, pageNumber, title);
        }
    }

    /**
     * 测试文件名清理功能
     */
    public void testFileNameSanitization() {
        String[] testFileNames = {
                "附录A 设备清单",
                "附录B 操作流程图",
                "前言",
                "1 范围",
                "6.1 设备分级",
                "附录",
                "Appendix A",
                "A 设备清单",
                "包含特殊字符的标题!@#$%^&*()",
                "",
                null,
                "很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的标题名称"
        };

        for (String fileName : testFileNames) {
            String sanitized = sanitizeFileName(fileName);
            log.info("原始文件名: [{}] -> 清理后: [{}]", fileName, sanitized);
        }
    }

    /**
     * 清理目录文本中的域代码
     */
    private String cleanTocText(String text) {
        if (text == null) return "";

        String cleaned = text;

        // 移除TOC域代码及其参数
        cleaned = cleaned.replaceAll("TOC\\s+\\\\[^\\s]*\\s*\"[^\"]*\"\\s*\\\\[^\\s]*\\s*\\\\[^\\s]*", "");
        cleaned = cleaned.replaceAll("TOC\\s+\\\\o\\s+\"[^\"]*\"\\s+\\\\[^\\s]*\\s+\\\\[^\\s]*", "");
        cleaned = cleaned.replaceAll("TOC\\s+[^\\u4e00-\\u9fa5]*", "");

        // 移除反斜杠转义序列
        cleaned = cleaned.replaceAll("\\\\[a-zA-Z]", "");
        cleaned = cleaned.replaceAll("\\\\\"", "");
        cleaned = cleaned.replaceAll("\\\\\\\\", "");

        // 移除Word域代码的特殊字符
        cleaned = cleaned.replaceAll("\\x13", ""); // 域开始标记
        cleaned = cleaned.replaceAll("\\x14", ""); // 域分隔符
        cleaned = cleaned.replaceAll("\\x15", ""); // 域结束标记

        // 移除其他可能的乱码字符
        cleaned = cleaned.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        // 移除不可见字符和控制字符
        cleaned = cleaned.replaceAll("\\p{Cntrl}", "");

        // 清理空格
        cleaned = cleaned.replaceAll("\\s+", " ").trim();

        return cleaned;
    }

    /**
     * 提取段落的纯文本内容，过滤Word域代码和格式信息
     */
    private String extractCleanText(Paragraph para) {
        try {
            StringBuilder cleanText = new StringBuilder();

            // 遍历段落中的所有运行（Run）
            for (Run run : para.getRuns()) {
                String runText = run.getText();
                if (runText != null && !runText.trim().isEmpty()) {
                    cleanText.append(runText);
                }
            }

            String text = cleanText.toString();

            // 如果Run方式获取的文本为空，尝试直接获取段落文本
            if (text.trim().isEmpty()) {
                text = para.getText();
            }

            // 使用专门的清理函数
            text = cleanTocText(text);

            // 移除其他Word域代码
            text = text.replaceAll("HYPERLINK\\s+[^\\s]+", "");
            text = text.replaceAll("PAGEREF\\s+[^\\s]+", "");
            text = text.replaceAll("_Toc\\d+", "");
            text = text.replaceAll("MERGEFORMAT", "");

            // 移除域代码的开始和结束标记
            text = text.replaceAll("\\x13", ""); // 域开始标记
            text = text.replaceAll("\\x14", ""); // 域分隔符
            text = text.replaceAll("\\x15", ""); // 域结束标记

            // 额外的乱码字符清理
            text = cleanupGarbledText(text);

            // 移除多余的空格和特殊字符，但保留必要的空格
            text = text.replaceAll("\\s+", " ");
            text = text.trim();

            // 记录调试信息
            log.debug("段落文本提取: 原始=[{}], 清理后=[{}]", para.getText(), text);

            return text;

        } catch (Exception e) {
            log.debug("提取段落文本失败: {}", e.getMessage());
            String fallbackText = para.getText().trim();
            // 对备用文本也进行清理
            fallbackText = cleanupGarbledText(fallbackText);
            log.debug("使用备用文本: [{}]", fallbackText);
            return fallbackText;
        }
    }

    /**
     * 清理乱码文本
     */
    private String cleanupGarbledText(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 移除不可见字符和控制字符
        text = text.replaceAll("\\p{Cntrl}", "");

        // 移除特定的乱码字符
        text = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        // 移除一些常见的Word乱码字符
        text = text.replaceAll("[\uFEFF\uFFFE\uFFFF]", ""); // BOM和其他特殊字符
        text = text.replaceAll("[\u200B-\u200F\u2028-\u202F]", ""); // 零宽字符和其他空白字符

        // 移除私有使用区字符（可能是乱码）
        text = text.replaceAll("[\uE000-\uF8FF]", "");

        // 移除一些可能的Word特殊字符
        text = text.replaceAll("[\u0001-\u0008\u000B\u000C\u000E-\u001F]", "");

        return text;
    }

    /**
     * 构建层级结构
     */
    private List<JSONObject> buildHierarchicalStructure(List<JSONObject> flatToc) {
        List<JSONObject> result = new ArrayList<>();
        List<JSONObject> stack = new ArrayList<>();

        for (JSONObject item : flatToc) {
            JSONObject tocItem = new JSONObject();
            tocItem.put("title", item.getString("title"));
            tocItem.put("pageNumber", item.getString("pageNumber"));
            tocItem.put("level", item.getInteger("level"));
            tocItem.put("children", new ArrayList<>());

            int currentLevel = item.getInteger("level");

            // 调整栈，确保当前项的父级在栈顶
            while (!stack.isEmpty() &&
                    stack.get(stack.size() - 1).getInteger("level") >= currentLevel) {
                stack.remove(stack.size() - 1);
            }

            if (stack.isEmpty()) {
                // 顶级项目
                result.add(tocItem);
            } else {
                // 添加到父级的children中
                JSONObject parent = stack.get(stack.size() - 1);
                parent.getJSONArray("children").add(tocItem);
            }

            stack.add(tocItem);
        }

        return result;
    }

    /**
     * 查找文档中的目录页段落
     */
    private List<Paragraph> findTableOfContentsPage(Document sourceDoc) {
        List<Paragraph> tocParagraphs = new ArrayList<>();
        try {
            NodeCollection paragraphs = sourceDoc.getChildNodes(NodeType.PARAGRAPH, true);
            boolean foundTocStart = false;
            boolean foundTocEnd = false;

            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph para = (Paragraph) paragraphs.get(i);
                String text = para.getText().trim().replaceAll(" ", "");

                // 查找目录页开始标志
                if (!foundTocStart && (text.equals("目次") || text.equals("目录") || text.equals("TABLE OF CONTENTS"))) {
                    foundTocStart = true;
                    tocParagraphs.add(para);
                    log.info("找到目录页开始: {}", text);
                    continue;
                }

                // 如果已经找到目录开始，继续收集段落
                if (foundTocStart && !foundTocEnd) {
                    tocParagraphs.add(para);

                    // 检查是否到达目录页结束（通常是遇到新的章节或页面）
                    if (isEndOfTocPage(text, para)) {
                        foundTocEnd = true;
                        log.info("目录页结束于: {}", text);
                        break;
                    }
                }
            }

            // 如果没有找到明确的结束标志，但找到了开始，则取一定数量的段落
            if (foundTocStart && !foundTocEnd && tocParagraphs.size() > 50) {
                tocParagraphs = tocParagraphs.subList(0, 50); // 限制在50个段落内
            }

        } catch (Exception e) {
            log.error("查找目录页时出错: {}", e.getMessage());
        }

        return tocParagraphs;
    }

    /**
     * 判断是否到达目录页结束
     */
    private boolean isEndOfTocPage(String text, Paragraph para) {
        // 如果遇到明显的章节开始标志
        if (text.matches("^第[一二三四五六七八九十\\d]+章.*") ||
                text.matches("^第[一二三四五六七八九十\\d]+节.*") ||
                text.matches("^[1-9]\\d*\\..*") ||
                text.matches("^前\\s*言.*") ||
                text.matches("^序\\s*言.*")) {
            return true;
        }

        // 检查段落样式是否为正文样式（非目录样式）
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style != null) {
                String styleName = style.getName().toLowerCase();
                if (styleName.contains("normal") || styleName.contains("正文")) {
                    return true;
                }
            }
        } catch (Exception e) {
            // 忽略样式检查错误
        }

        return false;
    }

    /**
     * 解析目录项文本，提取标题、级别和页码
     */
    private JSONObject parseTocEntry(String text, int index) {
        try {
            // 添加调试日志
            log.debug("解析目录项 {}: 文本=[{}], 长度={}, 是否匹配模式={}",
                    index, text, text.length(), containsValidTocPattern(text));

            // 跳过明显不是目录项的文本
            if (text.length() < 2 || !containsValidTocPattern(text)) {
                log.debug("目录项 {} 被跳过: 长度不足或不匹配模式", index);
                return null;
            }

            JSONObject tocEntry = new JSONObject();

            // 解析层级关系（通过缩进或编号判断）
            int level = determineTocLevel(text);

            // 提取页码（通常在行末）
            String pageNumber = extractTocPageNumber(text);

            // 提取标题（去除页码和点线）
            String title = extractTocTitle(text, pageNumber);

            if (title.isEmpty()) {
                return null;
            }

            tocEntry.put("title", title);
            tocEntry.put("level", level);
            tocEntry.put("pageNumber", pageNumber);
            tocEntry.put("index", index);
            tocEntry.put("originalText", text);

            return tocEntry;

        } catch (Exception e) {
            log.debug("解析目录项失败: {} - {}", text, e.getMessage());
            return null;
        }
    }

    /**
     * 检查文本是否包含有效的目录模式
     */
    private boolean containsValidTocPattern(String text) {
        // 记录调试信息
        log.debug("检查目录模式: [{}]", text);

        // 先检查特殊的目录项（前言、序言等）- 更宽松的匹配
        if (text.contains("前言") || text.contains("序言") ||
                text.matches(".*前\\s*言.*") || text.matches(".*序\\s*言.*")) {
            log.debug("匹配特殊目录项: 前言/序言");
            return true;
        }

        // 检查是否包含页码（阿拉伯数字）
        if (text.matches(".*\\d+\\s*$")) {
            log.debug("匹配阿拉伯数字页码");
            return true;
        }

        // 检查是否包含罗马数字页码
        if (text.matches(".*[IVX]+\\s*$")) {
            log.debug("匹配罗马数字页码");
            return true;
        }

        // 检查是否包含目录常见的编号模式
        if (text.matches("^\\s*\\d+.*") || // 数字开头
                text.matches("^\\s*\\d+\\.\\d+.*") || // 如 6.1
                text.matches("^\\s*[一二三四五六七八九十]+.*") || // 中文数字
                text.matches("^\\s*第[一二三四五六七八九十\\d]+章.*") || // 第X章
                text.matches("^\\s*附录.*") || // 附录
                text.matches("^\\s*Appendix.*") || // 英文附录
                text.matches("^\\s*[A-Z]\\s+.*") || // 单字母开头（如 A 附录）
                text.matches("^\\s*[A-Z]\\d*\\s+.*")) { // 字母数字组合（如 A1 附录）
            log.debug("匹配编号模式");
            return true;
        }

        // 检查是否包含点线（目录中常见的连接线）
        if (text.contains("…") || text.contains("....") || text.contains("···") ||
                text.contains("．．．") || text.contains("..")) {
            log.debug("匹配点线连接符");
            return true;
        }

        // 检查是否包含常见的目录关键词
        if (text.matches(".*(?:范围|引用|定义|总则|职责|要求|管理|内容|方法|程序|流程|记录|报告).*")) {
            log.debug("匹配目录关键词");
            return true;
        }

        // 更宽松的匹配：如果文本长度合理且包含中文字符，也认为可能是目录项
        if (text.length() >= 2 && text.length() <= 100 && text.matches(".*[\\u4e00-\\u9fa5].*")) {
            log.debug("匹配中文内容（宽松模式）");
            return true;
        }

        log.debug("未匹配任何目录模式");
        return false;
    }

    /**
     * 确定目录项的层级
     */
    private int determineTocLevel(String text) {
        // 通过前导空格数量判断层级
        int leadingSpaces = 0;
        for (char c : text.toCharArray()) {
            if (c == ' ' || c == '\t') {
                leadingSpaces++;
            } else {
                break;
            }
        }

        String trimmedText = text.trim();

        // 根据编号模式判断层级
        if (trimmedText.matches("^\\d+\\s+.*")) { // 如 "1 范围"
            return 1;
        } else if (trimmedText.matches("^\\d+\\.\\d+\\s+.*")) { // 如 "6.1 设备分级"
            return 2;
        } else if (trimmedText.matches("^\\d+\\.\\d+\\.\\d+\\s+.*")) { // 如 "6.1.1 xxx"
            return 3;
        } else if (trimmedText.matches("^\\d+\\.\\d+\\.\\d+\\.\\d+\\s+.*")) { // 如 "6.1.1.1 xxx"
            return 4;
        } else if (trimmedText.matches("^第[一二三四五六七八九十\\d]+章.*")) { // 第X章
            return 1;
        } else if (AppendixTextProcessor.isAppendixContent(trimmedText)) { // 使用专门的附录识别
            return AppendixTextProcessor.determineAppendixLevel(trimmedText);
        } else if (leadingSpaces > 10) { // 较多缩进
            return 3;
        } else if (leadingSpaces > 5) { // 中等缩进
            return 2;
        }

        return 1; // 默认为1级
    }

    /**
     * 从目录项中提取页码
     */
    private String extractTocPageNumber(String text) {
        // 匹配行末的数字（页码）
        if (text.matches(".*\\d+\\s*$")) {
            String[] parts = text.trim().split("\\s+");
            String lastPart = parts[parts.length - 1];
            if (lastPart.matches("\\d+")) {
                return lastPart;
            }
        }

        // 匹配罗马数字页码（如 II, III）
        if (text.matches(".*[IVX]+\\s*$")) {
            String[] parts = text.trim().split("\\s+");
            String lastPart = parts[parts.length - 1];
            if (lastPart.matches("[IVX]+")) {
                return lastPart;
            }
        }

        return "";
    }

    /**
     * 从目录项中提取标题
     */
    private String extractTocTitle(String text, String pageNumber) {
        String title = text.trim();

        // 首先使用专门的清理函数
        title = cleanTocText(title);

        // 移除页码（阿拉伯数字和罗马数字）
        if (!pageNumber.isEmpty()) {
            title = title.replaceAll("\\s+" + pageNumber + "\\s*$", "");
            // 处理罗马数字页码的特殊情况
            title = title.replaceAll(pageNumber + "\\s*$", "");
        }

        // 移除各种类型的点线连接符
        title = title.replaceAll("[…\\.]{3,}", " ");
        title = title.replaceAll("·{3,}", " ");
        title = title.replaceAll("．{3,}", " ");
        title = title.replaceAll("\\s*\\.{4,}\\s*", " ");

        // 特殊处理"前言"等词语中的空格
        title = title.replaceAll("前\\s+言", "前言");
        title = title.replaceAll("序\\s+言", "序言");

        // 特殊处理"附录"相关的标题
        title = cleanupAppendixTitle(title);

        // 移除开头和结尾的特殊字符，但保留字母和数字（用于附录编号）
        title = title.replaceAll("^[^\\u4e00-\\u9fa5\\dA-Za-z]+", "");
        title = title.replaceAll("[^\\u4e00-\\u9fa5\\dA-Za-z\\s\\.]+$", "");

        // 清理多余的空格
        title = title.replaceAll("\\s+", " ").trim();

        return title;
    }

    /**
     * 清理附录标题中的乱码和特殊字符
     */
    private String cleanupAppendixTitle(String title) {
        if (title == null || title.isEmpty()) {
            return title;
        }

        // 使用专门的附录文本处理器
        return AppendixTextProcessor.cleanAppendixText(title);
    }

    /**
     * 获取标题的级别
     */
    private int getHeadingLevel(Paragraph para) {
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style == null) {
                return 1;
            }

            String styleName = style.getName().toLowerCase();

            // 尝试从样式名称中提取级别
            if (styleName.matches(".*[1-6].*")) {
                for (int i = 1; i <= 6; i++) {
                    if (styleName.contains(String.valueOf(i))) {
                        return i;
                    }
                }
            }

            // WPS Office的数字样式ID映射
            if (styleName.equals("4")) return 1;
            if (styleName.equals("5")) return 2;
            if (styleName.equals("6")) return 3;
            if (styleName.equals("7")) return 4;
            if (styleName.equals("8")) return 5;
            if (styleName.equals("9")) return 6;

            // 默认返回1级标题
            return 1;
        } catch (Exception e) {
            return 1;
        }
    }

    /**
     * 从段落中提取页码信息
     */
    private String extractPageNumber(Paragraph para) {
        try {
            String text = para.getText();

            // 查找页码模式，通常在目录中以数字结尾
            if (text.matches(".*\\d+\\s*$")) {
                String[] parts = text.trim().split("\\s+");
                String lastPart = parts[parts.length - 1];
                if (lastPart.matches("\\d+")) {
                    return lastPart;
                }
            }

            // 查找制表符后的数字（目录中常见的格式）
            if (text.contains("\t")) {
                String[] tabParts = text.split("\t");
                String lastTabPart = tabParts[tabParts.length - 1].trim();
                if (lastTabPart.matches("\\d+")) {
                    return lastTabPart;
                }
            }

            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 检查是否为标题样式
     */
    private boolean isHeadingLevel(Paragraph para) {
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style == null) {
                return false;
            }

            String styleName = style.getName().toLowerCase();

            // 1. 匹配WPS Office的数字样式ID（4=标题1, 5=标题2, 6=标题3, 7=标题4）
            if (styleName.matches("[4-9]") ||
                    styleName.matches("heading\\s*[1-6]") ||
                    styleName.matches(".*标题\\s*[1-6].*") ||
                    styleName.matches(".*[一级二三四五六]级标题.*") ||
                    styleName.matches(".*title\\s*[1-6].*") ||
                    styleName.matches("heading[1-6]") ||
                    styleName.matches(".*标题[1-6].*")) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 将拆分后的Word文档与目录项进行匹配映射
     *
     * @param file 上传的Word文档
     * @param templateId 模板ID
     * @param formdataId 表单数据ID
     * @return 包含拆分文档、目录项和匹配映射关系的结果
     */
    public JSONObject splitDocumentAndMapWithTOC(MultipartFile file, Integer templateId, Integer formdataId) {
        JSONObject result = new JSONObject();

        try {
            // 1. 首先获取目录页中的所有目录项
            JSONObject tocFromPage = getDocumentTableOfContents(file);
            List<JSONObject> tocFromPageList = tocFromPage.getJSONArray("flatToc").toJavaList(JSONObject.class);

            // 2. 按标题拆分文档并提取基于标题样式的目录信息
            JSONObject splitResult = splitDocumentByHeadingsWithTOC(file, templateId, formdataId);
            List<JSONObject> splitDocuments = splitResult.getJSONArray("splitDocuments").toJavaList(JSONObject.class);
            List<JSONObject> tocFromHeadings = splitResult.getJSONArray("tableOfContents").toJavaList(JSONObject.class);

            // 3. 执行匹配映射
            List<JSONObject> mappingResults = performTocMapping(tocFromPageList, tocFromHeadings, splitDocuments);

            // 4. 组装返回结果
            result.put("splitDocuments", splitDocuments);
            result.put("tocFromPage", tocFromPageList);
            result.put("tocFromHeadings", tocFromHeadings);
            result.put("mappingResults", mappingResults);
            result.put("totalSplitDocuments", splitDocuments.size());
            result.put("totalTocFromPage", tocFromPageList.size());
            result.put("totalTocFromHeadings", tocFromHeadings.size());
            result.put("totalMappings", mappingResults.size());

            log.info("文档拆分和目录映射完成 - 拆分文档: {}, 目录页目录项: {}, 标题目录项: {}, 映射关系: {}",
                    splitDocuments.size(), tocFromPageList.size(), tocFromHeadings.size(), mappingResults.size());

        } catch (Exception e) {
            log.error("文档拆分和目录映射失败: {}", e.getMessage(), e);
            throw new RuntimeException("文档拆分和目录映射失败", e);
        }

        return result;
    }

    /**
     * 执行目录项与拆分文档的匹配映射
     *
     * @param tocFromPage     从目录页提取的目录项
     * @param tocFromHeadings 从标题样式提取的目录项
     * @param splitDocuments  拆分后的文档列表
     * @return 匹配映射结果列表
     */
    private List<JSONObject> performTocMapping(List<JSONObject> tocFromPage,
                                               List<JSONObject> tocFromHeadings,
                                               List<JSONObject> splitDocuments) {
        List<JSONObject> mappingResults = new ArrayList<>();

        log.info("开始执行目录映射 - 目录页项目: {}, 标题项目: {}, 拆分文档: {}",
                tocFromPage.size(), tocFromHeadings.size(), splitDocuments.size());

        // 为每个拆分的文档寻找对应的目录项
        for (JSONObject splitDoc : splitDocuments) {
            String fileName = splitDoc.getString("fileName");

            // 从文件名中提取标题（去除序号后缀）
            String docTitle = extractTitleFromFileName(fileName);

            JSONObject mapping = new JSONObject();
            mapping.put("splitDocument", splitDoc);
            mapping.put("docTitle", docTitle);
            mapping.put("tocPageMatch", null);
            mapping.put("tocHeadingMatch", null);
            mapping.put("matchScore", 0.0);
            mapping.put("matchType", "NONE");

            // 1. 尝试与目录页中的目录项匹配
            JSONObject bestTocPageMatch = findBestTocMatch(docTitle, tocFromPage);
            if (bestTocPageMatch != null) {
                mapping.put("tocPageMatch", bestTocPageMatch);
                double pageMatchScore = calculateMatchScore(docTitle, bestTocPageMatch.getString("title"));
                mapping.put("tocPageMatchScore", pageMatchScore);

                if (pageMatchScore > mapping.getDoubleValue("matchScore")) {
                    mapping.put("matchScore", pageMatchScore);
                    mapping.put("matchType", "TOC_PAGE");
                }
            }

            // 2. 尝试与标题样式目录项匹配
            JSONObject bestHeadingMatch = findBestTocMatch(docTitle, tocFromHeadings);
            if (bestHeadingMatch != null) {
                mapping.put("tocHeadingMatch", bestHeadingMatch);
                double headingMatchScore = calculateMatchScore(docTitle, bestHeadingMatch.getString("title"));
                mapping.put("tocHeadingMatchScore", headingMatchScore);

                if (headingMatchScore > mapping.getDoubleValue("matchScore")) {
                    mapping.put("matchScore", headingMatchScore);
                    mapping.put("matchType", "TOC_HEADING");
                }
            }

            // 3. 如果两种匹配都存在，选择得分更高的
            if (bestTocPageMatch != null && bestHeadingMatch != null) {
                double pageScore = mapping.getDoubleValue("tocPageMatchScore");
                double headingScore = mapping.getDoubleValue("tocHeadingMatchScore");

                if (pageScore > headingScore) {
                    mapping.put("matchType", "TOC_PAGE_PREFERRED");
                } else if (headingScore > pageScore) {
                    mapping.put("matchType", "TOC_HEADING_PREFERRED");
                } else {
                    mapping.put("matchType", "BOTH_EQUAL");
                }
            }

            mappingResults.add(mapping);

            log.info("文档映射结果 - 文档: [{}], 最佳匹配: [{}], 得分: {}, 类型: {}",
                    docTitle,
                    mapping.get("matchType").equals("TOC_PAGE") || mapping.get("matchType").equals("TOC_PAGE_PREFERRED") ?
                            (bestTocPageMatch != null ? bestTocPageMatch.getString("title") : "无") :
                            (bestHeadingMatch != null ? bestHeadingMatch.getString("title") : "无"),
                    mapping.getDoubleValue("matchScore"),
                    mapping.getString("matchType"));
        }

        return mappingResults;
    }

    /**
     * 从文件名中提取标题（去除序号后缀）
     */
    private String extractTitleFromFileName(String fileName) {
        if (fileName == null) return "";

        // 去除文件扩展名
        String title = fileName;
        if (title.contains(".")) {
            title = title.substring(0, title.lastIndexOf("."));
        }

        // 去除末尾的序号（如 " - 0", " - 1" 等）
        if (title.matches(".*\\s*-\\s*\\d+$")) {
            title = title.replaceAll("\\s*-\\s*\\d+$", "");
        }

        return title.trim();
    }

    /**
     * 寻找最佳匹配的目录项
     */
    private JSONObject findBestTocMatch(String docTitle, List<JSONObject> tocList) {
        if (docTitle == null || docTitle.trim().isEmpty() || tocList == null || tocList.isEmpty()) {
            return null;
        }

        JSONObject bestMatch = null;
        double bestScore = 0.0;

        for (JSONObject tocItem : tocList) {
            String tocTitle = tocItem.getString("title");
            if (tocTitle == null || tocTitle.trim().isEmpty()) {
                continue;
            }

            double score = calculateMatchScore(docTitle, tocTitle);

            if (score > bestScore) {
                bestScore = score;
                bestMatch = tocItem;
            }
        }

        // 只返回得分超过阈值的匹配
        return bestScore >= 0.6 ? bestMatch : null;
    }

    /**
     * 计算两个标题之间的匹配得分
     */
    private double calculateMatchScore(String title1, String title2) {
        if (title1 == null || title2 == null) {
            return 0.0;
        }

        // 标准化标题（去除空格、标点符号等）
        String normalizedTitle1 = normalizeTitle(title1);
        String normalizedTitle2 = normalizeTitle(title2);

        // 1. 完全匹配
        if (normalizedTitle1.equals(normalizedTitle2)) {
            return 1.0;
        }

        // 2. 包含关系匹配
        if (normalizedTitle1.contains(normalizedTitle2) || normalizedTitle2.contains(normalizedTitle1)) {
            double containmentScore = Math.min(normalizedTitle1.length(), normalizedTitle2.length()) /
                    (double) Math.max(normalizedTitle1.length(), normalizedTitle2.length());
            return 0.8 * containmentScore;
        }

        // 3. 编辑距离匹配
        int editDistance = calculateEditDistance(normalizedTitle1, normalizedTitle2);
        int maxLength = Math.max(normalizedTitle1.length(), normalizedTitle2.length());

        if (maxLength == 0) {
            return 0.0;
        }

        double similarity = 1.0 - (double) editDistance / maxLength;

        // 4. 关键词匹配加分
        double keywordBonus = calculateKeywordMatchBonus(normalizedTitle1, normalizedTitle2);

        return Math.max(0.0, similarity + keywordBonus * 0.2);
    }

    /**
     * 标准化标题文本
     */
    private String normalizeTitle(String title) {
        if (title == null) return "";

        return title.trim()
                .replaceAll("\\s+", "")  // 去除所有空格
                .replaceAll("[\\p{Punct}]", "")  // 去除标点符号
                .toLowerCase();
    }

    /**
     * 计算编辑距离（Levenshtein距离）
     */
    private int calculateEditDistance(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();

        int[][] dp = new int[m + 1][n + 1];

        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }

        return dp[m][n];
    }

    /**
     * 计算关键词匹配加分
     */
    private double calculateKeywordMatchBonus(String title1, String title2) {
        // 定义一些重要的关键词
        String[] keywords = {"管理", "制度", "规定", "办法", "流程", "标准", "规范", "要求",
                "职责", "范围", "定义", "总则", "附录", "前言", "序言"};

        int matchCount = 0;
        for (String keyword : keywords) {
            if (title1.contains(keyword) && title2.contains(keyword)) {
                matchCount++;
            }
        }

        return matchCount > 0 ? (double) matchCount / keywords.length : 0.0;
    }

    /**
     * 合并文档
     *
     * @param templateId
     * @param formdataId
     */
    public void mergeDocuments(Integer templateId, Integer formdataId) {
        try {
            String sourcePath = path + File.separator + templateId + File.separator + formdataId;
            String targetPath = path + File.separator + templateId;

            File dir = new File(sourcePath);
            File[] docxFiles = dir.listFiles((d, name) -> name.toLowerCase().endsWith(".docx"));
            if (docxFiles == null || docxFiles.length == 0) {
                log.info("未找到任何.docx文件");
                return;
            }

            // 按文件名排序，确保合并顺序正确
            Arrays.sort(docxFiles, Comparator.comparing((File file) -> {
                String name = file.getName();
                int dashIndex = name.lastIndexOf("-");
                if (dashIndex != -1 && dashIndex < name.length() - 1) {
                    try {
                        String numStr = name.substring(dashIndex + 1, name.lastIndexOf("."));
                        return Integer.parseInt(numStr);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，按文件名排序
                        return Integer.MAX_VALUE;
                    }
                }
                return Integer.MAX_VALUE;
            }));

            // 打印排序后的文件列表
            log.info("找到以下文档，将按以下顺序合并：");
            for (int i = 0; i < docxFiles.length; i++) {
                System.out.println((i + 1) + ". " + docxFiles[i].getName());
                log.info("{}.{}", i + 1, docxFiles[i].getName());
            }

            // 初始化 Document 对象
            Document doc = new Document(docxFiles[0].getAbsolutePath());
            log.info("基础文档: {}", docxFiles[0].getName());

            // 从第二个文件开始合并（跳过第一个，因为它已经是基础文档）
            for (int i = 1; i < docxFiles.length; i++) {
                Document document = new Document(docxFiles[i].getAbsolutePath());
                System.out.println("正在合并: " + docxFiles[i].getName());

                // 判断是否需要分页：目录(0)和前言(1)之间需要分页
                boolean needsPageBreak = (i == 1); // 只在前言文档前添加分页

                // 使用改进的合并方法
                appendDocument(doc, document, needsPageBreak);
            }

            // 保存合并的文档，使用时间戳避免文件占用
            String finalOutputPath = targetPath + File.separator + "result.docx";
            doc.save(finalOutputPath);
            System.out.println("文档合并完成，保存至: " + finalOutputPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void appendDocument(Document dstDoc, Document srcDoc, boolean needsPageBreak) throws Exception {
        if (needsPageBreak) {
            // 保留分页符：设置为新页开始
            srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.NEW_PAGE);
            dstDoc.appendDocument(srcDoc, ImportFormatMode.KEEP_SOURCE_FORMATTING);
        } else {
            // 连续合并：设置为连续模式，避免分页
            srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.CONTINUOUS);
            dstDoc.appendDocument(srcDoc, ImportFormatMode.USE_DESTINATION_STYLES);
        }
    }


    public static void registerWord2412() {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
