package ltd.zstech.standard.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文档映射结果实体类
 * 用于表示拆分文档与目录项的匹配映射关系
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentMappingResult {

    /**
     * 拆分后的文档信息
     */
    private JSONObject splitDocument;

    /**
     * 从文档标题提取的标题
     */
    private String docTitle;

    /**
     * 从目录页匹配的目录项
     */
    private JSONObject tocPageMatch;

    /**
     * 从标题样式匹配的目录项
     */
    private JSONObject tocHeadingMatch;

    /**
     * 目录页匹配得分
     */
    private Double tocPageMatchScore;

    /**
     * 标题样式匹配得分
     */
    private Double tocHeadingMatchScore;

    /**
     * 最终匹配得分
     */
    private Double matchScore;

    /**
     * 匹配类型
     * NONE - 无匹配
     * TOC_PAGE - 目录页匹配
     * TOC_HEADING - 标题样式匹配
     * TOC_PAGE_PREFERRED - 目录页匹配优先
     * TOC_HEADING_PREFERRED - 标题样式匹配优先
     * BOTH_EQUAL - 两种匹配得分相等
     */
    private String matchType;

    /**
     * 是否匹配成功（得分超过阈值）
     */
    private Boolean isMatched;

    /**
     * 匹配的目录项标题
     */
    private String matchedTocTitle;

    /**
     * 匹配的目录项级别
     */
    private Integer matchedTocLevel;

    /**
     * 匹配的目录项页码
     */
    private String matchedTocPageNumber;

    /**
     * 构造函数，用于快速创建映射结果
     */
    public DocumentMappingResult(JSONObject splitDocument, String docTitle) {
        this.splitDocument = splitDocument;
        this.docTitle = docTitle;
        this.matchScore = 0.0;
        this.matchType = "NONE";
        this.isMatched = false;
    }

    /**
     * 设置目录页匹配结果
     */
    public void setTocPageMatch(JSONObject tocPageMatch, Double score) {
        this.tocPageMatch = tocPageMatch;
        this.tocPageMatchScore = score;
        updateBestMatch();
    }

    /**
     * 设置标题样式匹配结果
     */
    public void setTocHeadingMatch(JSONObject tocHeadingMatch, Double score) {
        this.tocHeadingMatch = tocHeadingMatch;
        this.tocHeadingMatchScore = score;
        updateBestMatch();
    }

    /**
     * 更新最佳匹配结果
     */
    private void updateBestMatch() {
        double pageScore = tocPageMatchScore != null ? tocPageMatchScore : 0.0;
        double headingScore = tocHeadingMatchScore != null ? tocHeadingMatchScore : 0.0;

        if (pageScore > headingScore) {
            this.matchScore = pageScore;
            this.matchType = "TOC_PAGE";
            if (tocPageMatch != null) {
                this.matchedTocTitle = tocPageMatch.getString("title");
                this.matchedTocLevel = tocPageMatch.getInteger("level");
                this.matchedTocPageNumber = tocPageMatch.getString("pageNumber");
            }
        } else if (headingScore > pageScore) {
            this.matchScore = headingScore;
            this.matchType = "TOC_HEADING";
            if (tocHeadingMatch != null) {
                this.matchedTocTitle = tocHeadingMatch.getString("title");
                this.matchedTocLevel = tocHeadingMatch.getInteger("level");
                this.matchedTocPageNumber = tocHeadingMatch.getString("pageNumber");
            }
        } else if (pageScore > 0 && headingScore > 0) {
            this.matchScore = pageScore; // 相等时选择目录页匹配
            this.matchType = "BOTH_EQUAL";
            if (tocPageMatch != null) {
                this.matchedTocTitle = tocPageMatch.getString("title");
                this.matchedTocLevel = tocPageMatch.getInteger("level");
                this.matchedTocPageNumber = tocPageMatch.getString("pageNumber");
            }
        } else {
            this.matchScore = 0.0;
            this.matchType = "NONE";
        }

        // 判断是否匹配成功（阈值为0.6）
        this.isMatched = this.matchScore >= 0.6;

        // 如果有多个匹配且得分接近，更新匹配类型
        if (tocPageMatchScore != null && tocHeadingMatchScore != null) {
            double diff = Math.abs(pageScore - headingScore);
            if (diff < 0.1) { // 得分差异小于0.1认为相等
                this.matchType = "BOTH_EQUAL";
            } else if (pageScore > headingScore) {
                this.matchType = "TOC_PAGE_PREFERRED";
            } else {
                this.matchType = "TOC_HEADING_PREFERRED";
            }
        }
    }

    /**
     * 获取最佳匹配的目录项
     */
    public JSONObject getBestMatch() {
        if ("TOC_PAGE".equals(matchType) || "TOC_PAGE_PREFERRED".equals(matchType) || "BOTH_EQUAL".equals(matchType)) {
            return tocPageMatch;
        } else if ("TOC_HEADING".equals(matchType) || "TOC_HEADING_PREFERRED".equals(matchType)) {
            return tocHeadingMatch;
        }
        return null;
    }

    /**
     * 转换为JSON对象
     */
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("splitDocument", splitDocument);
        json.put("docTitle", docTitle);
        json.put("tocPageMatch", tocPageMatch);
        json.put("tocHeadingMatch", tocHeadingMatch);
        json.put("tocPageMatchScore", tocPageMatchScore);
        json.put("tocHeadingMatchScore", tocHeadingMatchScore);
        json.put("matchScore", matchScore);
        json.put("matchType", matchType);
        json.put("isMatched", isMatched);
        json.put("matchedTocTitle", matchedTocTitle);
        json.put("matchedTocLevel", matchedTocLevel);
        json.put("matchedTocPageNumber", matchedTocPageNumber);
        return json;
    }
}
