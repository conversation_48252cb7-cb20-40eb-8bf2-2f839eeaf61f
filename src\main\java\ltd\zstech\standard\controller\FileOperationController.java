package ltd.zstech.standard.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.common.api.vo.Result;
import ltd.zstech.common.aspect.annotation.AutoLog;
import ltd.zstech.modules.commonservice.service.FormOperationService;
import ltd.zstech.modules.commonservice.service.ICustomFormTemplateService;
import ltd.zstech.modules.views.entity.FormCustomFormTemplate;
import ltd.zstech.standard.entity.FileDetailVo;
import ltd.zstech.standard.request.FileSplitRequest;
import ltd.zstech.standard.service.FileOperationService;
import ltd.zstech.standard.util.DuokeAPI;
import ltd.zstech.standard.util.MultiUserTokenManager;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/fileOperation")
@Slf4j
public class FileOperationController {

    @Resource
    private FormOperationService formOperationService;
    @Resource
    private ICustomFormTemplateService customFormTemplateService;
    @Resource
    private DuokeAPI duokeAPI;
    @Resource
    private MultiUserTokenManager multiUserTokenManager;
    @Resource
    private FileOperationService fileOperationService;

    @PostMapping(value = "/splitWithMapping")
    @ApiOperation(value = "文档结构化并进行目录映射")
    @AutoLog
    public Result<?> splitDocumentWithMapping(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        Integer formdataId = request.getFormdataId();
        Integer subTemplateId = request.getSubTemplateId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }
        Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
        // 获取文件id，根据文件id从多可获取到文件地址
        String fid = formById.get("fid").toString();
        // String fid = "X00035";
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);
        FileDetailVo fileDetail = duokeAPI.getFileDetail(accessToken, fid);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            // MultipartFile file = new MockMultipartFile("file", "QSN—WJ—1—101—2024设备缺陷管理制度.docx", "text/plain", stream);
            MultipartFile file = new MockMultipartFile("file", fileDetail.getName(), "text/plain", stream);
            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }

            // 执行文档拆分和目录映射
            JSONObject mappingResult = fileOperationService.splitDocumentAndMapWithTOC(file, templateId, formdataId);
            List<JSONObject> mappingResults = mappingResult.getJSONArray("mappingResults").toJavaList(JSONObject.class);

            // 拆分成功之后，对目次进行映射：
            FormCustomFormTemplate subFormTemplate = customFormTemplateService.getById(subTemplateId);
            List<Map<String, Object>> formData = formOperationService.getFormData(subFormTemplate.getFormtablename(), "pid=" + formdataId);

            // 使用映射结果进行更智能的匹配
            for (JSONObject mappingResult1 : mappingResults) {
                JSONObject splitDoc = mappingResult1.getJSONObject("splitDocument");
                String fileName = splitDoc.getString("fileName");
                String filePath = splitDoc.getString("filePath");
                String docTitle = mappingResult1.getString("docTitle");

                // 获取最佳匹配的目录项
                JSONObject bestMatch = null;
                String matchType = mappingResult1.getString("matchType");
                if ("TOC_PAGE".equals(matchType) || "TOC_PAGE_PREFERRED".equals(matchType)) {
                    bestMatch = mappingResult1.getJSONObject("tocPageMatch");
                } else if ("TOC_HEADING".equals(matchType) || "TOC_HEADING_PREFERRED".equals(matchType)) {
                    bestMatch = mappingResult1.getJSONObject("tocHeadingMatch");
                } else if ("BOTH_EQUAL".equals(matchType)) {
                    // 如果两者得分相等，优先使用目录页的匹配
                    bestMatch = mappingResult1.getJSONObject("tocPageMatch");
                }

                for (Map<String, Object> formDatum : formData) {
                    String mcmc = formDatum.get("Mcmc").toString();
                    String cleanFileName = getFileNameWithoutExtension(fileName);

                    // 多种匹配策略
                    boolean matched = false;

                    // 1. 直接文件名匹配
                    if (cleanFileName.equals(mcmc)) {
                        matched = true;
                    }
                    // 2. 使用提取的文档标题匹配
                    else if (docTitle.equals(mcmc)) {
                        matched = true;
                    }
                    // 3. 使用最佳匹配的目录项标题匹配
                    else if (bestMatch != null && bestMatch.getString("title").equals(mcmc)) {
                        matched = true;
                    }
                    // 4. 模糊匹配（包含关系）
                    else if (mcmc.contains(docTitle) || docTitle.contains(mcmc)) {
                        matched = true;
                    }

                    if (matched) {
                        formDatum.put("WJ", filePath);
                        Object id = formDatum.get("ID");
                        formDatum.remove("ID");
                        formOperationService.update(subFormTemplate.getFormtablename(), "ID=" + id, formDatum);

                        log.info("成功匹配并更新: 文档[{}] -> 目录项[{}], 匹配类型: {}",
                                docTitle, mcmc, matchType);
                        break;
                    }
                }
            }

            // 切分完成之后，更新制度状态为待发布
            formById.put("Zt", "待发布");
            Object id = formById.get("ID");
            formById.remove("ID");
            formOperationService.update(formTemplate.getFormtablename(), "ID=" + id, formById);

            // 返回详细的映射结果
            JSONObject response = new JSONObject();
            response.put("message", "文档结构化和目录映射完成");
            response.put("mappingDetails", mappingResult);

            return Result.ok(response);
        } catch (Exception e) {
            log.error("文档结构化和目录映射失败", e);
            return Result.error("文档结构化和目录映射失败: " + e.getMessage());
        }
    }

    @PostMapping(value = "/split")
    @ApiOperation(value = "文档结构化")
    @AutoLog
    public Result<?> splitDocument(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        Integer formdataId = request.getFormdataId();
        Integer subTemplateId = request.getSubTemplateId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }
        Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
        // 获取文件id，根据文件id从多可获取到文件地址
        // String fid = formById.get("fid").toString();
        String fid = "X00035";
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MockMultipartFile("file", "QSN—WJ—1—101—2024设备缺陷管理制度.docx", "text/plain", stream);
            // MultipartFile file = new MockMultipartFile("file", fileDetail.getName(), "text/plain", stream);
            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }
            // 切分后的文件上传到服务器
            List<JSONObject> jsonObjects = fileOperationService.splitDocumentByHeadings(file, templateId, formdataId);
            // 拆分成功之后，对目次进行映射：
            FormCustomFormTemplate subFormTemplate = customFormTemplateService.getById(subTemplateId);
            List<Map<String, Object>> formData = formOperationService.getFormData(subFormTemplate.getFormtablename(), "pid=" + formdataId);
            for (JSONObject jsonObject : jsonObjects) {
                String fileName = jsonObject.getString("fileName");
                String filePath = jsonObject.getString("filePath");

                for (Map<String, Object> formDatum : formData) {
                    String mcmc = formDatum.get("Mcmc").toString();
                    fileName = getFileNameWithoutExtension(fileName);
                    if (fileName.equals(mcmc)) {
                        formDatum.put("WJ", filePath);
                        Object id = formDatum.get("ID");
                        formDatum.remove("ID");
                        formOperationService.update(subFormTemplate.getFormtablename(), "ID=" + id, formDatum);
                    }
                }
            }

            // 切分完成之后，更新制度状态为待发布
            formById.put("Zt", "待发布");
            Object id = formById.get("ID");
            formById.remove("ID");
            formOperationService.update(formTemplate.getFormtablename(), "ID=" + id, formById);

            // 切分后进行合并
            // fileOperationService.mergeDocuments(templateId, formdataId);
            return Result.ok("文档结构化完成");
        } catch (Exception e) {
            log.error("文档结构化失败", e);
            return Result.error("文档结构化失败: " + e.getMessage());
        }
    }

    private boolean isWordDocument(MultipartFile file) {
        String contentType = file.getContentType();
        String fileName = file.getOriginalFilename();

        return (contentType != null &&
                contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                || (fileName != null && fileName.toLowerCase().endsWith(".docx"));
    }

    @PostMapping(value = "/getTableOfContents")
    @ApiOperation(value = "获取文档目录信息")
    @AutoLog
    public Result<?> getTableOfContents(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }

        // 获取文件id，根据文件id从多可获取到文件地址
        // String fid = formById.get("fid").toString();
        String fid = "X00035";
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MockMultipartFile("file", "QSN—WJ—1—101—2024设备缺陷管理制度.docx", "text/plain", stream);

            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }

            // 获取目录页中的目录信息
            JSONObject tocFromPage = fileOperationService.getDocumentTableOfContents(file);

            return Result.ok(tocFromPage);
        } catch (Exception e) {
            log.error("获取文档目录信息失败", e);
            return Result.error("获取文档目录信息失败: " + e.getMessage());
        }
    }

    @PostMapping(value = "/previewMapping")
    @ApiOperation(value = "预览文档拆分和目录映射结果")
    @AutoLog
    public Result<?> previewMapping(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        Integer formdataId = request.getFormdataId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }

        // 获取文件id，根据文件id从多可获取到文件地址
        String fid = "X00035";
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MockMultipartFile("file", "QSN—WJ—1—101—2024设备缺陷管理制度.docx", "text/plain", stream);

            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }

            // 执行文档拆分和目录映射（仅预览，不保存文件）
            JSONObject mappingResult = fileOperationService.splitDocumentAndMapWithTOC(file, templateId, formdataId);

            // 添加统计信息
            JSONObject statistics = new JSONObject();
            List<JSONObject> mappingResults = mappingResult.getJSONArray("mappingResults").toJavaList(JSONObject.class);

            int totalMappings = mappingResults.size();
            int successfulMappings = 0;
            int tocPageMappings = 0;
            int tocHeadingMappings = 0;
            int noMappings = 0;

            for (JSONObject mapping : mappingResults) {
                String matchType = mapping.getString("matchType");
                double matchScore = mapping.getDoubleValue("matchScore");

                if (matchScore > 0.6) {
                    successfulMappings++;
                }

                switch (matchType) {
                    case "TOC_PAGE":
                    case "TOC_PAGE_PREFERRED":
                        tocPageMappings++;
                        break;
                    case "TOC_HEADING":
                    case "TOC_HEADING_PREFERRED":
                        tocHeadingMappings++;
                        break;
                    case "BOTH_EQUAL":
                        tocPageMappings++;
                        tocHeadingMappings++;
                        break;
                    case "NONE":
                        noMappings++;
                        break;
                }
            }

            statistics.put("totalMappings", totalMappings);
            statistics.put("successfulMappings", successfulMappings);
            statistics.put("tocPageMappings", tocPageMappings);
            statistics.put("tocHeadingMappings", tocHeadingMappings);
            statistics.put("noMappings", noMappings);
            statistics.put("successRate", totalMappings > 0 ? (double) successfulMappings / totalMappings : 0.0);

            mappingResult.put("statistics", statistics);

            return Result.ok(mappingResult);
        } catch (Exception e) {
            log.error("预览文档拆分和目录映射失败", e);
            return Result.error("预览文档拆分和目录映射失败: " + e.getMessage());
        }
    }

    private String getFileNameWithoutExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf("-");
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        } else {
            return fileName;
        }
    }
}
