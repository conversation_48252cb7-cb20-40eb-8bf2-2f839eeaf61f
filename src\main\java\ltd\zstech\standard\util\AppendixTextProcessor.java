package ltd.zstech.standard.util;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * 附录文本处理工具类
 * 专门处理附录相关的文本清理、识别和格式化
 */
@Slf4j
public class AppendixTextProcessor {

    // 附录识别模式
    private static final Pattern[] APPENDIX_PATTERNS = {
            Pattern.compile("^\\s*附录.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("^\\s*Appendix.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("^\\s*[A-Z]\\s+.*"),
            Pattern.compile("^\\s*[A-Z]\\d*\\s+.*"),
            Pattern.compile("^\\s*[A-Z]\\.\\d+.*"),
            Pattern.compile("^\\s*App\\s*\\..*", Pattern.CASE_INSENSITIVE)
    };

    // 乱码字符模式
    private static final Pattern[] GARBLED_PATTERNS = {
            Pattern.compile("[\uFEFF\uFFFE\uFFFF]"), // BOM和特殊字符
            Pattern.compile("[\u200B-\u200F\u2028-\u202F]"), // 零宽字符
            Pattern.compile("[\uE000-\uF8FF]"), // 私有使用区
            Pattern.compile("\\?+"), // 问号乱码
            Pattern.compile("□+"), // 方框乱码
            Pattern.compile("\\x{FFFD}+"), // 替换字符
            Pattern.compile("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"), // 控制字符
            Pattern.compile("\\p{Cntrl}") // 其他控制字符
    };

    /**
     * 检查文本是否为附录相关内容
     */
    public static boolean isAppendixContent(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        String cleanText = text.trim();

        for (Pattern pattern : APPENDIX_PATTERNS) {
            if (pattern.matcher(cleanText).matches()) {
                log.debug("匹配附录模式: [{}] -> {}", cleanText, pattern.pattern());
                return true;
            }
        }

        return false;
    }

    /**
     * 清理附录文本中的乱码
     */
    public static String cleanAppendixText(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        String cleaned = text;

        // 移除各种乱码字符
        for (Pattern pattern : GARBLED_PATTERNS) {
            cleaned = pattern.matcher(cleaned).replaceAll("");
        }

        // 特殊处理附录标题格式
        cleaned = normalizeAppendixTitle(cleaned);

        // 清理多余空格
        cleaned = cleaned.replaceAll("\\s+", " ").trim();

        if (!cleaned.equals(text)) {
            log.debug("附录文本清理: [{}] -> [{}]", text, cleaned);
        }

        return cleaned;
    }

    /**
     * 标准化附录标题格式
     */
    private static String normalizeAppendixTitle(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        String normalized = text;

        // 处理中文附录格式
        normalized = normalized.replaceAll("附\\s*录\\s*([A-Z])\\s*", "附录$1 ");
        normalized = normalized.replaceAll("附\\s*录\\s*([A-Z]\\d*)\\s*", "附录$1 ");
        normalized = normalized.replaceAll("附\\s*录\\s*", "附录 ");

        // 处理英文附录格式
        normalized = normalized.replaceAll("Appendix\\s+([A-Z])\\s*", "Appendix $1 ");
        normalized = normalized.replaceAll("App\\s*\\.\\s*([A-Z])\\s*", "Appendix $1 ");

        // 处理单字母附录格式
        normalized = normalized.replaceAll("^\\s*([A-Z])\\s+", "$1 ");
        normalized = normalized.replaceAll("^\\s*([A-Z]\\d*)\\s+", "$1 ");

        return normalized;
    }

    /**
     * 提取附录编号
     */
    public static String extractAppendixNumber(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        String cleanText = text.trim();

        // 匹配中文附录编号
        if (cleanText.matches("^附录\\s*([A-Z]\\d*).*")) {
            return cleanText.replaceAll("^附录\\s*([A-Z]\\d*).*", "$1");
        }

        // 匹配英文附录编号
        if (cleanText.matches("^Appendix\\s+([A-Z]\\d*).*")) {
            return cleanText.replaceAll("^Appendix\\s+([A-Z]\\d*).*", "$1");
        }

        // 匹配单字母编号
        if (cleanText.matches("^([A-Z]\\d*)\\s+.*")) {
            return cleanText.replaceAll("^([A-Z]\\d*)\\s+.*", "$1");
        }

        return "";
    }

    /**
     * 提取附录标题（去除编号）
     */
    public static String extractAppendixTitle(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        String cleanText = cleanAppendixText(text);

        // 移除中文附录编号
        cleanText = cleanText.replaceAll("^附录\\s*[A-Z]\\d*\\s*", "");

        // 移除英文附录编号
        cleanText = cleanText.replaceAll("^Appendix\\s+[A-Z]\\d*\\s*", "");
        cleanText = cleanText.replaceAll("^App\\.\\s*[A-Z]\\d*\\s*", "");

        // 移除单字母编号
        cleanText = cleanText.replaceAll("^[A-Z]\\d*\\s+", "");

        // 移除页码
        cleanText = cleanText.replaceAll("\\s+\\d+\\s*$", "");
        cleanText = cleanText.replaceAll("\\s+[IVX]+\\s*$", "");

        // 移除点线连接符
        cleanText = cleanText.replaceAll("[…\\.]{3,}", " ");
        cleanText = cleanText.replaceAll("·{3,}", " ");
        cleanText = cleanText.replaceAll("．{3,}", " ");

        return cleanText.trim();
    }

    /**
     * 判断附录级别
     */
    public static int determineAppendixLevel(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 1;
        }

        String cleanText = text.trim();

        // A.1.1 格式 - 3级
        if (cleanText.matches("^[A-Z]\\.\\d+\\.\\d+\\s+.*")) {
            return 3;
        }

        // A.1 格式 - 2级
        if (cleanText.matches("^[A-Z]\\.\\d+\\s+.*")) {
            return 2;
        }

        // 附录A、Appendix A、A 格式 - 1级
        if (isAppendixContent(cleanText)) {
            return 1;
        }

        return 1;
    }

    /**
     * 生成标准化的附录文件名
     */
    public static String generateAppendixFileName(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "appendix";
        }

        String number = extractAppendixNumber(text);
        String title = extractAppendixTitle(text);

        StringBuilder fileName = new StringBuilder();

        // 如果原文本已经包含"附录"，就不要重复添加
        if (text.contains("附录")) {
            // 直接使用清理后的标题
            if (!title.isEmpty()) {
                fileName.append(title);
            } else if (!number.isEmpty()) {
                fileName.append("附录").append(number);
            } else {
                fileName.append("附录");
            }
        } else {
            // 原文本不包含"附录"，需要添加
            if (!number.isEmpty()) {
                fileName.append("附录").append(number);
            } else {
                fileName.append("附录");
            }

            if (!title.isEmpty()) {
                fileName.append("_").append(title);
            }
        }

        // 清理文件名中的非法字符
        String result = fileName.toString();
        result = result.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_");
        result = result.replaceAll("_{2,}", "_");
        result = result.replaceAll("^_+|_+$", "");

        // 限制长度
        if (result.length() > 50) {
            result = result.substring(0, 50);
        }

        return result.isEmpty() ? "appendix" : result;
    }

    /**
     * 验证附录文本是否有效
     */
    public static boolean isValidAppendixText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        String cleanText = cleanAppendixText(text);

        // 检查是否还有有效内容
        if (cleanText.isEmpty()) {
            return false;
        }

        // 检查是否包含有意义的字符
        if (!cleanText.matches(".*[\\u4e00-\\u9fa5A-Za-z0-9].*")) {
            return false;
        }

        return true;
    }

    /**
     * 获取附录处理统计信息
     */
    public static String getProcessingStats(String originalText, String processedText) {
        if (originalText == null && processedText == null) {
            return "无文本";
        }

        int originalLength = originalText != null ? originalText.length() : 0;
        int processedLength = processedText != null ? processedText.length() : 0;
        int removedChars = originalLength - processedLength;

        return String.format("原始长度: %d, 处理后长度: %d, 移除字符: %d",
                originalLength, processedLength, removedChars);
    }
}
